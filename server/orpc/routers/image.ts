import { z } from "zod";
import { protectedProcedure } from "../init";
import { getUUIDString, sleep } from "@/lib/utils";
import { saveToR2 } from "@/server/r2.server";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { genGemini2_5FromFal } from "@/server/ai/gemini.server";
import { getDB } from "@/server/db/db-client.server";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_EDIT_IMAGE, EVENT_EDIT_IMAGE_WITH_TOOL } from "@/lib/track-events";
import { constructError<PERSON>al<PERSON><PERSON>, handleApiErrorEvent } from "@/@types/error-api";
import { handleUnifiedError } from "@/@types/error";

export const imageRouter = {
	editImage: protectedProcedure
		.input(
			z.object({
				prompt: z.string().min(1, "Prompt is required"),
				images: z.array(z.string().url("Invalid image URL")),
			}),
		)
		.handler(async ({ input, context }) => {
			return {
				resultUrls: ["https://static.editpal.im/mkt/home/<USER>"],
			};
			try {
				// 验证用户身份
				const userId = context.sessionUser?.id!;
				// 检查用户积分
				const needCredits = 5;
				const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});

				console.log("editImage params: ", input);
				console.log("creditConsumes: ", creditConsumes);

				// 追踪事件
				mixpanelTrackEvent(EVENT_EDIT_IMAGE, userId, {
					mp_country_code: context.cfIpCountryCode,
					ip: context.cfIp,
					membershipLevel: membershipLevel,
				});

				// 调用 AI 服务生成图片
				const resultUrls: string[] = await genGemini2_5FromFal(input.prompt, 1, input.images);

				// 保存到 R2
				const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					const [media]: MediaHead[] = await tx
						.insert(mediaHeadSchema)
						.values({
							uid: imageResultId,
							userId: userId,
							status: MediaResultStatus.Completed,
							type: MediaHeadType.Image,
							tool: MediaHeadToolType.ImageEditor,
							visibility: false,
							prompt: input.prompt,
							creditsSources: JSON.stringify(creditConsumes),
						})
						.returning();

					await tx.insert(mediaItemSchema).values(
						imagePaths.map((imagePath, index) => ({
							uid: getUUIDString(),
							userId: userId,
							mediaHeadUid: imageResultId,
							visibility: false,
							mediaPath: imagePath,
						})),
					);
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image generation result head uid: ${imageResultId}.`,
				});

				// 返回结果
				return {
					resultUrls: imagePaths.map((path) => `${OSS_URL_HOST}/${path}`),
				};
			} catch (error) {
				const finalError = constructErrorFalAI(error);
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.editImage`);
				throw handleUnifiedError(finalError, "rpc");
			}
		}),

	removeObject: protectedProcedure
		.input(
			z.object({
				prompt: z.string().min(1, "Prompt is required"),
				image: z.string().url("Invalid image URL"),
				tool: z.string().optional().default("image-editor"),
			}),
		)
		.handler(async ({ input, context }) => {
			try {
				// 验证用户身份
				const userId = context.sessionUser?.id!;
				// 检查用户积分
				const needCredits = 5;
				const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});

				console.log("removeObject params: ", input);
				console.log("creditConsumes: ", creditConsumes);

				// 追踪事件
				mixpanelTrackEvent(EVENT_EDIT_IMAGE_WITH_TOOL, userId, {
					mp_country_code: context.cfIpCountryCode,
					ip: context.cfIp,
					membershipLevel: membershipLevel,
					tool: input.tool,
				});

				// 调用 AI 服务生成图片
				const resultUrls: string[] = await genGemini2_5FromFal(input.prompt, 1, [input.image]);

				// 保存到 R2
				const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					const [media]: MediaHead[] = await tx
						.insert(mediaHeadSchema)
						.values({
							uid: imageResultId,
							userId: userId,
							status: MediaResultStatus.Completed,
							type: MediaHeadType.Image,
							tool: MediaHeadToolType.ImageEditor,
							visibility: false,
							prompt: input.prompt,
							creditsSources: JSON.stringify(creditConsumes),
						})
						.returning();

					await tx.insert(mediaItemSchema).values(
						imagePaths.map((imagePath, index) => ({
							uid: getUUIDString(),
							userId: userId,
							mediaHeadUid: imageResultId,
							visibility: false,
							mediaPath: imagePath,
						})),
					);
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image generation result head uid: ${imageResultId}.`,
				});

				// 返回结果
				const resultUrl = `${OSS_URL_HOST}/${imagePaths[0]}`;
				return {
					resultUrl: resultUrl,
					imageResultId: imageResultId,
				};
			} catch (error) {
				const finalError = constructErrorFalAI(error);
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.removeObject`);
				throw handleUnifiedError(finalError, "rpc");
				// return handleApiErrorRPC(finalError, `${WEBNAME} - rpc: image.removeObject`);
			}
		}),

	removeBackground: protectedProcedure
		.input(
			z.object({
				imageUrl: z.string().url(),
			}),
		)
		.handler(async ({ input, context }) => {
			try {
				const userId = context.sessionUser?.id!;

				const resultUrls: string[] = await genGemini2_5FromFal("", 1, [input.imageUrl]);
				return {
					url: resultUrls[0],
				};

				// const falClient = await getFalClient("", ctx);

				// const result = await falClient.subscribe("fal-ai/bria/background/remove", {
				// 	input: {
				// 		image_url: input.imageUrl,
				// 		sync_mode: true,
				// 	},
				// });

				// return {
				// 	url: result.data.image.url,
				// };
			} catch (error) {
				const finalError = constructErrorFalAI(error, "Failed to remove background");
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.removeBackground`);
				throw handleUnifiedError(finalError, "rpc");
			}
		}),

	generateTextToImage: protectedProcedure
		.input(
			z.object({
				prompt: z.string(),
				seed: z.number().optional(),
				imageSize: z.enum(["landscape_4_3", "portrait_4_3", "square", "landscape_16_9", "portrait_16_9"]).optional(),
			}),
		)
		.handler(async ({ input, context }) => {
			try {
				await sleep(1000 * 2);
				return {
					url: "https://static.editpal.im/mkt/home/<USER>",
					width: 1024,
					height: 1024,
					seed: -1,
				};

				// const falClient = await getFalClient("", ctx);

				// const result = await falClient.subscribe("fal-ai/flux-kontext-lora/text-to-image", {
				// 	input: {
				// 		prompt: input.prompt,
				// 		image_size: input.imageSize || "square",
				// 		num_inference_steps: 30,
				// 		guidance_scale: 2.5,
				// 		num_images: 1,
				// 		enable_safety_checker: true,
				// 		output_format: "png",
				// 		seed: input.seed,
				// 	},
				// });

				// // Handle different possible response structures
				// const resultData = (result as any).data || result;
				// if (!resultData.images?.[0]) {
				// 	throw new Error("No image generated");
				// }

				// return {
				// 	url: resultData.images[0].url,
				// 	width: resultData.images[0].width,
				// 	height: resultData.images[0].height,
				// 	seed: resultData.seed,
				// };
			} catch (error) {
				const finalError = constructErrorFalAI(error, "Failed to generate image");
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.generateTextToImage`);
				throw handleUnifiedError(finalError, "rpc");
			}
		}),
};
