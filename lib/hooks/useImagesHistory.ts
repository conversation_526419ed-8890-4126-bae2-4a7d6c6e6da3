import { useCallback, useMemo, useRef, useState } from "react";
import type { PlacedImage } from "@/@types/canvas";

interface CommitOptions {
	label?: string;
}

interface UpdateOptions extends CommitOptions {
	commit?: boolean; // default true when label provided in convenience wrapper
}

interface HistoryEntry {
	images: PlacedImage[];
	label?: string;
}

export function useImagesHistory(initial: PlacedImage[] = []) {
	const [images, _setImages] = useState<PlacedImage[]>(initial);
	const [history, setHistory] = useState<HistoryEntry[]>([{ images: initial }]);
	const [historyIndex, setHistoryIndex] = useState<number>(0);

	// keep a ref to always read latest state inside callbacks
	const imagesRef = useRef(images);
	imagesRef.current = images;

	const canUndo = historyIndex > 0;
	const canRedo = historyIndex < history.length - 1;

	const setImages: React.Dispatch<React.SetStateAction<PlacedImage[]>> = useCallback((updater) => {
		_setImages((prev) => {
			const next = typeof updater === "function" ? (updater as (p: PlacedImage[]) => PlacedImage[])(prev) : updater;
			return next;
		});
	}, []);

	const commit = useCallback(
		(opts?: CommitOptions) => {
			const snapshot = imagesRef.current;
			setHistory((prev) => {
				const trunc = prev.slice(0, historyIndex + 1);
				const nextEntry: HistoryEntry = { images: [...snapshot], label: opts?.label };
				const next = [...trunc, nextEntry];
				return next;
			});
			setHistoryIndex((idx) => idx + 1);
		},
		[historyIndex],
	);

	const reset = useCallback((next: PlacedImage[]) => {
		_setImages(next);
		setHistory([{ images: [...next] }]);
		setHistoryIndex(0);
	}, []);

	const update = useCallback(
		(updateFn: (prev: PlacedImage[]) => PlacedImage[], opts?: UpdateOptions) => {
			_setImages((prev) => updateFn(prev));
			if (opts?.commit) {
				// commit based on the next microtask to ensure state applied
				queueMicrotask(() => commit({ label: opts?.label }));
			}
		},
		[commit],
	);

	const setDirect = useCallback(
		(next: PlacedImage[], opts?: UpdateOptions) => {
			_setImages(next);
			if (opts?.commit) {
				queueMicrotask(() => commit({ label: opts?.label }));
			}
		},
		[commit],
	);

	const undo = useCallback(() => {
		if (!canUndo) return;
		const target = history[historyIndex - 1];
		_setImages(target.images);
		setHistoryIndex(historyIndex - 1);
	}, [canUndo, history, historyIndex]);

	const redo = useCallback(() => {
		if (!canRedo) return;
		const target = history[historyIndex + 1];
		_setImages(target.images);
		setHistoryIndex(historyIndex + 1);
	}, [canRedo, history, historyIndex]);

	const historyLength = history.length;

	return useMemo(
		() => ({
			images,
			setImages, // non-committing set
			update, // functional update, optionally commit
			setDirect, // direct set, optionally commit
			commit,
			reset,
			undo,
			redo,
			canUndo,
			canRedo,
			historyIndex,
			historyLength,
		}),
		[images, setImages, update, setDirect, commit, reset, undo, redo, canUndo, canRedo, historyIndex, historyLength],
	);
}
